package services

import (
	"context"
	"testing"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MockDB 模拟数据库
type MockDB struct {
	mock.Mock
}

func (m *MockDB) WithContext(ctx context.Context) *gorm.DB {
	args := m.Called(ctx)
	return args.Get(0).(*gorm.DB)
}

// TestFailedCKBlacklist 测试故障CK黑名单功能
func TestFailedCKBlacklist(t *testing.T) {
	// 创建测试用的绑卡处理器
	logger := zap.NewNop()
	config := &config.Config{}
	
	processor := &BindCardProcessor{
		logger:            logger,
		config:            config,
		failedCKBlacklist: make(map[uint]time.Time),
		blacklistTTL:      5 * time.Minute,
	}

	t.Run("添加CK到黑名单", func(t *testing.T) {
		ckID := uint(9114)
		reason := "请先去登录"
		
		// 添加到黑名单
		processor.addToFailedCKBlacklist(ckID, reason)
		
		// 验证CK在黑名单中
		assert.True(t, processor.isInFailedCKBlacklist(ckID))
	})

	t.Run("黑名单过期清理", func(t *testing.T) {
		ckID := uint(9115)
		reason := "测试过期"
		
		// 设置短TTL
		processor.blacklistTTL = 100 * time.Millisecond
		
		// 添加到黑名单
		processor.addToFailedCKBlacklist(ckID, reason)
		
		// 验证CK在黑名单中
		assert.True(t, processor.isInFailedCKBlacklist(ckID))
		
		// 等待过期
		time.Sleep(150 * time.Millisecond)
		
		// 验证CK不再在黑名单中
		assert.False(t, processor.isInFailedCKBlacklist(ckID))
	})

	t.Run("多个CK黑名单管理", func(t *testing.T) {
		processor.blacklistTTL = 5 * time.Minute
		
		// 添加多个CK到黑名单
		processor.addToFailedCKBlacklist(1001, "错误1")
		processor.addToFailedCKBlacklist(1002, "错误2")
		processor.addToFailedCKBlacklist(1003, "错误3")
		
		// 验证所有CK都在黑名单中
		assert.True(t, processor.isInFailedCKBlacklist(1001))
		assert.True(t, processor.isInFailedCKBlacklist(1002))
		assert.True(t, processor.isInFailedCKBlacklist(1003))
		
		// 验证不在黑名单中的CK
		assert.False(t, processor.isInFailedCKBlacklist(1004))
	})
}

// TestFindAnyAvailableCKWithBlacklist 测试查找可用CK时排除黑名单
func TestFindAnyAvailableCKWithBlacklist(t *testing.T) {
	// 这个测试需要真实的数据库连接，这里只是示例结构
	t.Skip("需要真实数据库连接的集成测试")
	
	// 示例测试逻辑：
	// 1. 创建测试数据库和CK记录
	// 2. 将某些CK添加到黑名单
	// 3. 调用findAnyAvailableCK方法
	// 4. 验证返回的CK不在黑名单中
}

// BenchmarkBlacklistOperations 黑名单操作性能测试
func BenchmarkBlacklistOperations(b *testing.B) {
	processor := &BindCardProcessor{
		logger:            zap.NewNop(),
		failedCKBlacklist: make(map[uint]time.Time),
		blacklistTTL:      5 * time.Minute,
	}

	b.Run("添加到黑名单", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			processor.addToFailedCKBlacklist(uint(i), "测试错误")
		}
	})

	b.Run("检查黑名单", func(b *testing.B) {
		// 先添加一些CK到黑名单
		for i := 0; i < 1000; i++ {
			processor.addToFailedCKBlacklist(uint(i), "测试错误")
		}
		
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			processor.isInFailedCKBlacklist(uint(i % 1000))
		}
	})
}
