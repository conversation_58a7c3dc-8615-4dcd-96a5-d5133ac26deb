# CK黑名单配置示例
ck_management:
  blacklist:
    # 黑名单TTL - CK在黑名单中保留的时间
    ttl: "5m"  # 5分钟
    
    # 最大黑名单大小（0表示无限制）
    # 建议设置合理上限，避免内存/Redis占用过多
    max_size: 10000
    
    # 清理间隔 - 定期清理过期条目的间隔
    cleanup_interval: "1m"
    
    # 是否启用分布式黑名单（使用Redis）
    enable_distributed: true
    
    # Redis键前缀
    redis_key_prefix: "walmart_ck_blacklist"

# 不同环境的推荐配置：

# 开发环境
development:
  ck_management:
    blacklist:
      ttl: "2m"           # 较短的TTL，便于测试
      max_size: 100       # 较小的限制
      cleanup_interval: "30s"
      enable_distributed: false  # 可以使用内存黑名单
      redis_key_prefix: "dev_ck_blacklist"

# 测试环境  
testing:
  ck_management:
    blacklist:
      ttl: "3m"
      max_size: 1000
      cleanup_interval: "1m"
      enable_distributed: true
      redis_key_prefix: "test_ck_blacklist"

# 生产环境
production:
  ck_management:
    blacklist:
      ttl: "10m"          # 较长的TTL，避免频繁重试故障CK
      max_size: 50000     # 较大的限制，支持大规模部署
      cleanup_interval: "5m"
      enable_distributed: true
      redis_key_prefix: "prod_ck_blacklist"

# 高并发环境
high_concurrency:
  ck_management:
    blacklist:
      ttl: "15m"          # 更长的TTL
      max_size: 100000    # 更大的限制
      cleanup_interval: "10m"
      enable_distributed: true
      redis_key_prefix: "hc_ck_blacklist"

# 内存优化配置（适用于内存受限环境）
memory_optimized:
  ck_management:
    blacklist:
      ttl: "3m"           # 较短TTL，快速释放
      max_size: 5000      # 较小限制
      cleanup_interval: "30s"  # 频繁清理
      enable_distributed: true
      redis_key_prefix: "mem_opt_ck_blacklist"
